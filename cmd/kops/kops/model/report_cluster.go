package model

import (
	"bytes"
	"encoding/csv"
	"strconv"
	"strings"
	"time"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"git.woa.com/kateway/kateway-server/pkg/tmp/types/sets"
)

// ClusterReport 定义了集群健康检查报告的结构
// 包含集群的基本信息和健康统计分析数据
type ClusterReport struct {
	Template string
	ExpireAt string

	ClusterInfo

	// 嵌入的健康统计和分析数据结构
	Statistics ClusterHealthStatistics `gorm:"embedded"`
	Analysises ClusterHealthAnalysises `gorm:"embedded"`
}

func (c ClusterReport) CSV() []byte {
	var b bytes.Buffer
	writer := csv.NewWriter(transform.NewWriter(&b, simplifiedchinese.GBK.NewEncoder()))

	// 写入 CSV 头部
	writer.Write([]string{"APPID", "集群ID", "集群名称", "CLBID", "使用协议", "风险个数", "风险等级", "关联资源", "风险详情", "排障链接"})

	for _, clb := range c.Analysises.Details {
		record := []string{
			strconv.Itoa(int(c.AppID)),
			c.ClusterID,
			c.ClusterName,
			clb.CLBID,
			clb.Protocols,
			strconv.Itoa(int(clb.RiskCount)),
			clb.RiskLevel,
			clb.UsingResources,
			strings.Join(clb.RiskInfos, ","),
			buildCLBRef(c.ClusterID, clb.CLBID),
		}
		writer.Write(record)
	}

	writer.Flush()
	return b.Bytes()
}

type ClusterInfo struct {
	ClusterName   string
	ClusterID     string
	ClusterRegion string
	ClusterType   string
	AppID         int64
	Description   string
	MetaClusterID string
	State         string

	ClusterBaseStats

	NetworkInfo
	ComponentInfo

	References ClusterReferences `gorm:"-" json:"-" yaml:"-"`

	// 数据库时间字段
	CreatedAt  time.Time  `gorm:"column:CreatedAt;not null" json:"-" yaml:"-"`
	UpdatedAt  time.Time  `gorm:"column:UpdatedAt;not null" json:"-" yaml:"-"`
	FinishedAt *time.Time `gorm:"column:FinishedAt" json:"-" yaml:"-"`
}

// TableName 返回表名
func (ClusterInfo) TableName() string {
	return "clusterinfo"
}

type ClusterBaseStats struct {
	TotalCLBCount              int
	TotalServiceCount          int
	TotalIngressCount          int
	TotalTKEServiceConfigCount int
}

type NetworkInfo struct {
	ServiceCIDR   string
	NetworkType   string
	KubeProxyMode string
	K8SVersion    string
	VpcID         string

	SubnetID string
}

type ComponentInfo struct {
	ServiceControllerImage   string
	ServiceControllerVersion string
	ServiceAvailableReplicas int
	ServiceExpectReplicas    int
	ServiceArgs              string
	ServiceImagePullPolicy   string
	ServiceResource          string
	ServiceConfigMap         string

	IngressControllerImage   string
	IngressControllerVersion string
	IngressAvailableReplicas int
	IngressExpectReplicas    int
	IngressArgs              string
	IngressImagePullPolicy   string
	IngressResource          string
	IngressConfigMap         string
}

type ClusterReferences struct {
	Quota                    string
	OSSCluster               string
	OSSUser                  string
	MonitorMetaComponent     string
	MonitorServiceController string
}

// ClusterHealthStatistics 包含了集群健康检查的统计数据
type ClusterHealthStatistics struct {
	CLB        CLBRStats       `gorm:"embedded" json:"CLB"`        // CLB相关统计
	Listener   ListenerStats   `gorm:"embedded" json:"Listener"`   // 监听器相关统计
	Rule       RuleStats       `gorm:"embedded" json:"Rule"`       // 规则相关统计
	RealServer RealServerStats `gorm:"embedded" json:"RealServer"` // 实际服务器相关统计
}

func ConvertClusterStats(id string, cluster ClusterHealthStatistics) ClusterStats {
	stats := ClusterStats{
		ClusterName:               id,
		CLBTotalCount:             int(cluster.CLB.TotalCount),
		CLBTotalZeroWeightCount:   int(cluster.CLB.TotalZeroWeightCount),
		CLBTotalUnhealthCount:     int(cluster.CLB.TotalUnhealthCount),
		CLBTotalNoRealServerCount: int(cluster.CLB.TotalNoRealServerCount),
		CLBTotalNotExistedCount:   int(cluster.CLB.TotalNotExistedCount),

		ListenerTotalCount:           int(cluster.Listener.TotalCount),
		ListenerTotalZeroWeightCount: int(cluster.Listener.TotalZeroWeightCount),
		ListenerTotalUnhealthCount:   int(cluster.Listener.TotalUnhealthCount),

		RuleTotalCount:           int(cluster.Rule.TotalCount),
		RuleTotalZeroWeightCount: int(cluster.Rule.TotalZeroWeightCount),
		RuleTotalUnhealthCount:   int(cluster.CLB.TotalZeroWeightCount),

		RealServerTotalCount:           int(cluster.RealServer.TotalCount),
		RealServerTotalZeroWeightCount: int(cluster.RealServer.TotalZeroWeightCount),
		RealServerTotalUnhealthCount:   int(cluster.RealServer.TotalUnhealthCount),
	}

	return stats
}

type ClusterStats struct {
	ClusterName string `gorm:"column:ClusterName" json:"ClusterName"`

	CLBTotalCount             int
	CLBTotalZeroWeightCount   int
	CLBTotalUnhealthCount     int
	CLBTotalNoRealServerCount int
	CLBTotalNotExistedCount   int

	ListenerTotalCount           int
	ListenerTotalZeroWeightCount int
	ListenerTotalUnhealthCount   int

	RuleTotalCount           int
	RuleTotalZeroWeightCount int
	RuleTotalUnhealthCount   int

	RealServerTotalCount           int
	RealServerTotalZeroWeightCount int
	RealServerTotalUnhealthCount   int

	CreatedAt time.Time `gorm:"column:CreatedAt;not null"` // 发布任务创建时间
	UpdatedAt time.Time `gorm:"column:UpdatedAt;not null"` // 发布任务更新时间
}

// TableName 返回表名
func (ClusterStats) TableName() string {
	return "clusterstats"
}

// ClusterHealthAnalysises 包含了集群健康检查的风险分析数据
type ClusterHealthAnalysises struct {
	TotalCLBLevelRiskCount      int             `gorm:"column:TotalCLBLevelRiskCount" json:"TotalCLBLevelRiskCount"`           // CLB级别风险总数
	TotalListenerLevelRiskCount int             `gorm:"column:TotalListenerLevelRiskCount" json:"TotalListenerLevelRiskCount"` // 监听器级别风险总数
	TotalRuleLevelRiskCount     int             `gorm:"column:TotalRuleLevelRiskCount" json:"TotalRuleLevelRiskCount"`         // 规则级别风险总数
	TotalRSLevelRiskCount       int             `gorm:"column:TotalRSLevelRiskCount" json:"TotalRSLevelRiskCount"`             // 实际服务器级别风险总数
	Details                     []CLBRiskDetail `gorm:"-" json:"Details"`                                                      // 风险详细信息，不存储在数据库中
}

// CLBRStats 定义了CLB的统计信息
type CLBRStats struct {
	TotalCount           uint `gorm:"column:CLBTotalCount" json:"TotalCount"`                     // CLB总数
	TotalZeroWeightCount uint `gorm:"column:CLBTotalZeroWeightCount" json:"TotalZeroWeightCount"` // 权重为零的CLB总数
	TotalUnhealthCount   uint `gorm:"column:CLBTotalUnhealthCount" json:"TotalUnhealthCount"`     // 不健康的CLB总数

	TotalNoRealServerCount uint `gorm:"column:TotalNoRealServerCount" json:"TotalNoRealServerCount"`
	TotalNotExistedCount   uint `gorm:"column:TotalNotExistedCount" json:"TotalNotExistedCount"`
}

// ListenerStats 定义了监听器的统计信息
type ListenerStats struct {
	TotalCount           uint `gorm:"column:ListenerTotalCount" json:"TotalCount"`                 // 监听器总数
	TotalZeroWeightCount uint `gorm:"column:" json:"TotalZeroWeightCount"`                         // 权重为零的监听器总数
	TotalUnhealthCount   uint `gorm:"column:ListenerTotalUnhealthCount" json:"TotalUnhealthCount"` // 不健康的监听器总数
}

// RuleStats 定义了规则的统计信息
type RuleStats struct {
	TotalCount           uint `gorm:"column:RuleTotalCount" json:"TotalCount"`                     // 规则总数
	TotalZeroWeightCount uint `gorm:"column:RuleTotalZeroWeightCount" json:"TotalZeroWeightCount"` // 权重为零的规则总数
	TotalUnhealthCount   uint `gorm:"column:RuleTotalUnhealthCount" json:"TotalUnhealthCount"`     // 不健康的规则总数
}

// RealServerStats 定义了实际服务器的统计信息
type RealServerStats struct {
	TotalCount           uint `gorm:"column:RealServerTotalCount" json:"TotalCount"`                     // 实际服务器总数
	TotalZeroWeightCount uint `gorm:"column:RealServerTotalZeroWeightCount" json:"TotalZeroWeightCount"` // 权重为零的实际服务器总数
	TotalUnhealthCount   uint `gorm:"column:RealServerTotalUnhealthCount" json:"TotalUnhealthCount"`     // 不健康的实际服务器总数
}

// CLBRiskDetail 定义了CLB风险的详细信息
type CLBRiskDetail struct {
	IsNotFound       bool `json:"-" yaml:"-"`
	IsZeroRealServer bool `json:"-" yaml:"-"`

	CLBID          string
	Protocols      string
	RiskScore      int             `json:"-" yaml:"-"`
	RiskInfos      []string        `json:"-" yaml:"-"`
	RiskLevel      string          `json:",omitempty" yaml:",omitempty"`
	RiskCount      uint            `json:",omitempty" yaml:",omitempty"`
	Counters       map[string]uint `json:",omitempty" yaml:",omitempty"`
	UsingResources string          `json:",omitempty" yaml:",omitempty"`

	// 可选字段，包含权重和健康状态的详细信息
	Weight *RSWeightDetail `json:",omitempty" yaml:",omitempty"`
	Health *RSHealthDetail `json:",omitempty" yaml:",omitempty"`

	LBRInfo *LoadBalancerResourceInfo `json:",omitempty" yaml:",omitempty"`
}

func (c CLBRiskDetail) ResourceInfo() string {
	results := []string{}
	if c.LBRInfo != nil {
		for _, lbrR := range c.LBRInfo.Resources {
			results = append(results, lbrR.Kind+": "+lbrR.Namespace+"/"+lbrR.Name)
		}
	}
	return strings.Join(results, "; ")
}

// RSWeightDetail 定义了实际服务器权重的详细信息
type RSWeightDetail struct {
	Protocols string

	FailedListenerSet sets.String
	FailedRuleSet     sets.String

	PartFailedListenerSet sets.String
	PartFailedRuleSet     sets.String

	TotalRSCount              int
	TotalForbiddenRSCount     int
	TotalListenerCount        int
	TotalAllDownlistenerCount int
	TotalRuleCount            int
	TotalAllDownRuleCount     int
}

// RSHealthDetail 定义了实际服务器健康的详细信息
type RSHealthDetail struct {
	Protocols string

	FailedListenerSet sets.String
	FailedRuleSet     sets.String

	PartFailedListenerSet sets.String
	PartFailedRuleSet     sets.String

	TotalRSCount              int
	TotalForbiddenRSCount     int
	TotalListenerCount        int
	TotalAllDownlistenerCount int
	TotalRuleCount            int
	TotalAllDownRuleCount     int
}
